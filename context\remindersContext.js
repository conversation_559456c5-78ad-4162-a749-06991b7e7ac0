import { createContext } from "react";
import useSWR from "swr";

const RemindersProvider = createContext();

const BASE_URL = "https://cardiomedai-api.onrender.com";
const USER_ID = 1; // Should come from user context in real app

const fetcher = async (url) => {
  const res = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }

  const data = await res.json();
  return data;
};

export const RemindersContext = ({ children }) => {
  // Medication Reminders
  const { 
    data: medicationReminders, 
    error: medicationError, 
    isLoading: medicationLoading, 
    mutate: mutateMedication 
  } = useSWR(`${BASE_URL}/reminders/${USER_ID}`, fetcher);

  // BP Check Reminders
  const { 
    data: bpReminders, 
    error: bpError, 
    isLoading: bpLoading, 
    mutate: mutateBP 
  } = useSWR(`${BASE_URL}/reminders/bp-reminders/${USER_ID}`, fetcher);

  // Doctor Appointment Reminders
  const { 
    data: doctorReminders, 
    error: doctorError, 
    isLoading: doctorLoading, 
    mutate: mutateDoctor 
  } = useSWR(`${BASE_URL}/reminders/doctor-appointments/${USER_ID}`, fetcher);

  // Workout Reminders
  const { 
    data: workoutReminders, 
    error: workoutError, 
    isLoading: workoutLoading, 
    mutate: mutateWorkout 
  } = useSWR(`${BASE_URL}/reminders/workouts/${USER_ID}`, fetcher);

  // Upcoming Reminders (next 24 hours)
  const { 
    data: upcomingMedication, 
    error: upcomingMedError, 
    isLoading: upcomingMedLoading, 
    mutate: mutateUpcomingMed 
  } = useSWR(`${BASE_URL}/reminders/upcoming/${USER_ID}?hours=24`, fetcher);

  const { 
    data: upcomingBP, 
    error: upcomingBPError, 
    isLoading: upcomingBPLoading, 
    mutate: mutateUpcomingBP 
  } = useSWR(`${BASE_URL}/reminders/bp-upcoming/${USER_ID}?hours=24`, fetcher);

  // Helper functions for API calls
  const createMedicationReminder = async (reminderData) => {
    const response = await fetch(`${BASE_URL}/reminders/?user_id=${USER_ID}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reminderData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create medication reminder');
    }
    
    const data = await response.json();
    mutateMedication(); // Refresh data
    return data;
  };

  const createBPReminder = async (reminderData) => {
    const response = await fetch(`${BASE_URL}/reminders/bp-reminder/?user_id=${USER_ID}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reminderData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create BP reminder');
    }
    
    const data = await response.json();
    mutateBP(); // Refresh data
    return data;
  };

  const createDoctorReminder = async (reminderData) => {
    const response = await fetch(`${BASE_URL}/reminders/doctor-appointment/?user_id=${USER_ID}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reminderData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create doctor appointment reminder');
    }
    
    const data = await response.json();
    mutateDoctor(); // Refresh data
    return data;
  };

  const createWorkoutReminder = async (reminderData) => {
    const response = await fetch(`${BASE_URL}/reminders/workout/?user_id=${USER_ID}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reminderData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create workout reminder');
    }
    
    const data = await response.json();
    mutateWorkout(); // Refresh data
    return data;
  };

  const markMedicationTaken = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/mark-taken/${reminderId}`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark medication as taken');
    }
    
    mutateMedication(); // Refresh data
    mutateUpcomingMed(); // Refresh upcoming data
    return response.json();
  };

  const markBPCompleted = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/bp-reminder/${reminderId}/complete`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark BP check as completed');
    }
    
    mutateBP(); // Refresh data
    mutateUpcomingBP(); // Refresh upcoming data
    return response.json();
  };

  const markDoctorCompleted = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/doctor-appointment/${reminderId}/complete`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark doctor appointment as completed');
    }
    
    mutateDoctor(); // Refresh data
    return response.json();
  };

  const markWorkoutCompleted = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/workout/${reminderId}/complete`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark workout as completed');
    }
    
    mutateWorkout(); // Refresh data
    return response.json();
  };

  // Delete functions
  const deleteMedicationReminder = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/reminder/${reminderId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete medication reminder');
    }
    
    mutateMedication(); // Refresh data
    return response.json();
  };

  const deleteBPReminder = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/bp-reminder/${reminderId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete BP reminder');
    }
    
    mutateBP(); // Refresh data
    return response.json();
  };

  const deleteDoctorReminder = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/doctor-appointment/${reminderId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete doctor appointment reminder');
    }
    
    mutateDoctor(); // Refresh data
    return response.json();
  };

  const deleteWorkoutReminder = async (reminderId) => {
    const response = await fetch(`${BASE_URL}/reminders/workout/${reminderId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete workout reminder');
    }
    
    mutateWorkout(); // Refresh data
    return response.json();
  };

  const value = {
    // Data
    medicationReminders,
    bpReminders,
    doctorReminders,
    workoutReminders,
    upcomingMedication,
    upcomingBP,
    
    // Loading states
    medicationLoading,
    bpLoading,
    doctorLoading,
    workoutLoading,
    upcomingMedLoading,
    upcomingBPLoading,
    
    // Errors
    medicationError,
    bpError,
    doctorError,
    workoutError,
    upcomingMedError,
    upcomingBPError,
    
    // Mutate functions
    mutateMedication,
    mutateBP,
    mutateDoctor,
    mutateWorkout,
    mutateUpcomingMed,
    mutateUpcomingBP,
    
    // API functions
    createMedicationReminder,
    createBPReminder,
    createDoctorReminder,
    createWorkoutReminder,
    markMedicationTaken,
    markBPCompleted,
    markDoctorCompleted,
    markWorkoutCompleted,
    deleteMedicationReminder,
    deleteBPReminder,
    deleteDoctorReminder,
    deleteWorkoutReminder,
  };

  return (
    <RemindersProvider.Provider value={value}>
      {children}
    </RemindersProvider.Provider>
  );
};

export default RemindersProvider;
