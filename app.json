{"expo": {"name": "cardio-med-ai", "slug": "cardio-med-ai", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "cardiomedai", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take photos of blood pressure monitors.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images of blood pressure monitors."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-web-browser"], "experiments": {"typedRoutes": true}}}