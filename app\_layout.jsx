import { Stack } from "expo-router";
import { BpReaderContext } from '../context/bpReadingsContext';
import { AverageBpContext } from '../context/averageReadings';
import { HealthAdvisorContext } from '../context/healthAdvisorContext';
import { RemindersContext } from '../context/remindersContext';

export default function RootLayout() {
  return (
    <BpReaderContext>
      <AverageBpContext>
        <HealthAdvisorContext>
          <RemindersContext>
            <Stack screenOptions={{ headerShown: false }} />
          </RemindersContext>
        </HealthAdvisorContext>
      </AverageBpContext>
    </BpReaderContext>
  );
}
