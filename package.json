{"name": "cardio-med-ai", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "cod-string-magic": "^1.0.2", "expo": "53.0.13", "expo-blur": "~14.1.5", "expo-camera": "16.1.9", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "2.3.0", "expo-linear-gradient": "14.1.5", "expo-linking": "~7.1.5", "expo-router": "5.1.1", "expo-splash-screen": "0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "0.4.5", "expo-system-ui": "5.0.9", "expo-web-browser": "14.2.0", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "swr": "^2.3.3", "twrnc": "^4.8.0", "undefined": "\\"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}